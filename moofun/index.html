<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <!-- Safari兼容性：优化视口设置 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <!-- Safari兼容性：添加Safari特定的meta标签 -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="format-detection" content="telephone=no" />

    <title>%VITE_TITLE%</title>

    <!-- Safari兼容性：预加载关键CSS -->
    <link rel="preload" href="/src/css/style.css" as="style" />
    <link rel="stylesheet" href="/src/css/style.css" />

    <!-- Safari兼容性：添加内联关键CSS以防止FOUC -->
    <style>
      /* 防止Safari中的闪烁 */
      #app {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100vw;
        height: 100vh;
        background: #000000;
        z-index: 1;
        touch-action: manipulation;
      }

      @media (min-width: 600px) {
        #app {
          max-width: 375px;
          width: 375px;
        }
      }

      /* Safari兼容性：确保body样式立即生效 */
      body {
        margin: 0;
        padding: 0;
        background: #000000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        overflow: hidden;
      }
    </style>
   

    <!-- Open Graph meta tags -->
    <meta property="og:title" content="%VITE_TITLE%" />
    <meta property="og:description" content="%VITE_DESCRIPTION%" />
    <meta property="og:image" content="%VITE_OG_IMAGE%" />
    <meta property="og:url" content="%VITE_URL%" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="%VITE_SITE_NAME%" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:type" content="image/png" />

    <!-- Twitter Card tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="%VITE_TITLE%" />
    <meta name="twitter:description" content="%VITE_DESCRIPTION%" />
    <meta name="twitter:image" content="%VITE_OG_IMAGE%" />
  </head>
  <body>
    <div id="app">
      <!-- Safari兼容性：提供初始加载内容，防止完全黑屏 -->
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #000000;
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        z-index: 9999;
      ">
        <div style="text-align: center;">
          <div style="
            width: 40px;
            height: 40px;
            border: 3px solid #333;
            border-top: 3px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
          "></div>
          <div>Loading...</div>
        </div>
        <style>
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        </style>
      </div>
    </div>

    <!-- Safari兼容性：添加模块加载错误处理 -->
    <script>
      // Safari兼容性：检测模块支持
      if (!('noModule' in HTMLScriptElement.prototype)) {
        console.warn('Module scripts not supported, loading fallback');
      }

      // Safari兼容性：全局错误处理
      window.addEventListener('error', function(e) {
        console.error('Global error:', e.error);
        if (e.filename && e.filename.includes('main.js')) {
          document.getElementById('app').innerHTML = `
            <div style="
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: #000000;
              display: flex;
              justify-content: center;
              align-items: center;
              color: white;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              z-index: 9999;
              padding: 20px;
              box-sizing: border-box;
            ">
              <div style="text-align: center; max-width: 300px;">
                <div style="font-size: 18px; margin-bottom: 10px;">⚠️ Loading Error</div>
                <div style="font-size: 14px; margin-bottom: 20px; opacity: 0.8;">Failed to load application</div>
                <button onclick="location.reload()" style="
                  background: #007AFF;
                  color: white;
                  border: none;
                  padding: 10px 20px;
                  border-radius: 6px;
                  cursor: pointer;
                  font-size: 14px;
                ">Retry</button>
              </div>
            </div>
          `;
        }
      });
    </script>

    <script type="module" src="/src/main.js"></script>
  </body>
</html>

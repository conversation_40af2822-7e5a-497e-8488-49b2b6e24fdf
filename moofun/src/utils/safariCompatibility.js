/**
 * Safari兼容性工具函数
 * 解决Safari浏览器特有的兼容性问题
 */

/**
 * 检测是否为Safari浏览器
 */
export function isSafari() {
    const ua = navigator.userAgent;
    return /Safari/.test(ua) && !/Chrome/.test(ua) && !/Chromium/.test(ua);
}

/**
 * 获取Safari版本
 */
export function getSafariVersion() {
    if (!isSafari()) return null;
    
    const match = navigator.userAgent.match(/Version\/(\d+(?:\.\d+)*)/);
    return match ? parseFloat(match[1]) : null;
}

/**
 * 修复Safari的视口高度问题
 * Safari中100vh包含地址栏高度，导致内容被遮挡
 */
export function fixSafariViewportHeight() {
    if (!isSafari()) return;
    
    function updateVH() {
        // 获取实际视口高度
        const vh = window.innerHeight * 0.01;
        // 设置CSS变量
        document.documentElement.style.setProperty('--vh', `${vh}px`);
        document.documentElement.style.setProperty('--app-height', `${window.innerHeight}px`);
    }
    
    // 初始设置
    updateVH();
    
    // 监听窗口大小变化（包括地址栏显示/隐藏）
    window.addEventListener('resize', updateVH);
    window.addEventListener('orientationchange', () => {
        // 延迟执行，等待方向改变完成
        setTimeout(updateVH, 100);
    });
    
    // Safari特有：监听滚动事件来检测地址栏变化
    let ticking = false;
    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(() => {
                updateVH();
                ticking = false;
            });
            ticking = true;
        }
    });
}

/**
 * 修复Safari的字体加载问题
 */
export function fixSafariFont() {
    if (!isSafari()) return;
    
    // 检查字体是否加载成功
    if (document.fonts && document.fonts.ready) {
        document.fonts.ready.then(() => {
            console.log('Fonts loaded successfully');
        }).catch((error) => {
            console.warn('Font loading failed, using fallback:', error);
            // 强制使用系统字体
            document.body.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        });
    }
}

/**
 * 修复Safari的CSS变量问题
 */
export function fixSafariCSSVariables() {
    if (!isSafari()) return;
    
    const safariVersion = getSafariVersion();
    
    // Safari 12以下版本对CSS变量支持有限
    if (safariVersion && safariVersion < 12) {
        console.warn('Old Safari version detected, applying CSS variable fallbacks');
        
        // 应用回退样式
        const style = document.createElement('style');
        style.textContent = `
            body {
                font-size: 16px !important;
            }
            .main-text {
                -webkit-text-stroke: 2px rgba(59, 59, 59, 1) !important;
                text-shadow: 0 1px 2px rgba(59, 59, 59, 1) !important;
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * 修复Safari的模块加载问题
 */
export function fixSafariModuleLoading() {
    if (!isSafari()) return;
    
    // 添加模块加载错误处理
    window.addEventListener('error', (event) => {
        if (event.filename && event.filename.includes('.js')) {
            console.error('Module loading error in Safari:', event.error);
            // 可以在这里添加重试逻辑或显示错误信息
        }
    });
    
    // 监听未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
        console.error('Unhandled promise rejection in Safari:', event.reason);
        // 防止默认的错误处理
        event.preventDefault();
    });
}

/**
 * 应用所有Safari兼容性修复
 */
export function applySafariCompatibilityFixes() {
    if (!isSafari()) {
        console.log('Not Safari browser, skipping compatibility fixes');
        return;
    }
    
    console.log('Applying Safari compatibility fixes...');
    
    // 应用所有修复
    fixSafariViewportHeight();
    fixSafariFont();
    fixSafariCSSVariables();
    fixSafariModuleLoading();
    
    console.log('Safari compatibility fixes applied');
}

/**
 * 检查Safari是否支持某个特性
 */
export function checkSafariFeatureSupport() {
    if (!isSafari()) return {};
    
    return {
        cssVariables: CSS.supports('color', 'var(--test)'),
        fontDisplay: CSS.supports('font-display', 'swap'),
        customProperties: 'CSS' in window && 'supports' in CSS,
        intersectionObserver: 'IntersectionObserver' in window,
        webGL: !!window.WebGLRenderingContext,
    };
}

@font-face {
    font-family: 'Goldman Sans';
    src: url('./fonts/GoldmanSans_Blk.ttf') format('truetype');
    font-weight: 900;
    font-style: normal;
    /* Safari兼容性：添加字体显示策略 */
    font-display: swap;
}


:root {
    /* Safari兼容性：添加回退值和更稳定的计算方式 */
    --vh: 1vh;
    --app-width: 100vw;
    --app-height: 100vh;
    --app-height-fallback: 100vh; /* Safari回退 */
    --base-unit: calc(100vw / 375); /* 简化计算，避免变量嵌套 */
    --base-unit-fallback: 2.67px; /* 基于375px宽度的回退值 */
}

@media (min-width: 600px) {
    :root {
        --app-width: min(100vw, 375px);
        --base-unit: 1px; /* 固定基础单位，避免复杂计算 */
    }
}

    /* Rammetto One /
    / global-styles.css or font-style.css */
    body {
        /* Safari兼容性：添加系统字体回退 */
        font-family: 'Urbanist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        /* Safari兼容性：使用回退值和简化计算 */
        font-size: calc(var(--base-unit, var(--base-unit-fallback)) * 16);
        font-size: 16px; /* Safari回退 */
        font-weight: 900;
        line-height: 1;
        /* Safari兼容性：添加webkit前缀 */
        -webkit-font-feature-settings: normal;
        font-feature-settings: normal;
        paint-order: stroke fill;
    }

    .main-text {
        color: #FFFFFF;
        /* Safari兼容性：简化文本描边和阴影计算 */
        -webkit-text-stroke: calc(var(--base-unit, 1px) * 2) rgba(59, 59, 59, 1);
        -webkit-text-stroke: 2px rgba(59, 59, 59, 1); /* Safari回退 */
        text-shadow: 0 calc(var(--base-unit, 1px) * 1) calc(var(--base-unit, 1px) * 2) rgba(59, 59, 59, 1);
        text-shadow: 0 1px 2px rgba(59, 59, 59, 1); /* Safari回退 */
    }

    body {
        background: #000000;
        margin: 0;
        padding: 0;
    }

    .main-text {
        color: #FFFFFF;
        font-weight: 600;
        /* -webkit-text-stroke: calc(var(--base-unit) * 2) #090909; */
        /* text-shadow: 0 calc(var(--base-unit) * 1) calc(var(--base-unit) * 2) rgba(0, 0, 0, 0.75); */
    }

    /* Add a universal selector to inherit text styles */
    * {
        font-family: inherit;
        font-size: inherit;
        font-weight: inherit;
        line-height: inherit;
        letter-spacing: inherit;
        color: inherit;
        paint-order: inherit;

    }

    .flex-center {
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
    }